package com.drex.customer.service.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class CustomerProperties {

    @Value("${customer.register.topic:T_DREX_CUSTOMER_REGISTER}")
    private String customerRegisterTopic;

    @Value("${activity.event.topic:T_DREX_ACTIVITY_EVENT}")
    private String activityEventTopic;

    @Value("${customer.http.pool.size:150}")
    private Integer httpPoolSize;

    @Value("${customer.activity.event.delay.seconds:1}")
    private Integer activityEventDelaySeconds;

}
