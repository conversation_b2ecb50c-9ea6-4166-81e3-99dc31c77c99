package com.drex.customer.service.socialPlatform.service.impl;

import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.socialPlatform.service.UserBindValidator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 默认用户绑定验证器实现
 * 使用userId字段进行验证（适用于Discord、TikTok、Instagram等平台）
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class DefaultUserBindValidatorImpl implements UserBindValidator {
    
    @Resource
    private CustomerBindService customerBindService;
    
    @Override
    public String getSupportedPlatform() {
        return "DEFAULT";
    }
    
    @Override
    public CustomerBind validateUserBinding(SocialUserInfo userInfo, String platform) {
        if (userInfo == null) {
            return null;
        }
        
        // 默认使用userId进行验证
        if (StringUtils.hasText(userInfo.getUserId())) {
            log.info("[DefaultUserBindValidator] Validating user binding by userId: {} for platform: {}", 
                userInfo.getUserId(), platform);
            return customerBindService.findBySocialUserIdOrName(
                userInfo.getUserId(), userInfo.getUsername(), platform);
        }
        
        return null;
    }
}
