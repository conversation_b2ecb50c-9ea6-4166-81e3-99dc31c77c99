package com.drex.customer.service.socialPlatform.service;

import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;

/**
 * 用户绑定验证器接口
 * 用于验证社交平台用户是否已被其他用户绑定
 * 
 * <AUTHOR>
 */
public interface UserBindValidator {
    
    /**
     * 获取支持的平台类型
     * @return 平台类型
     */
    String getSupportedPlatform();
    
    /**
     * 验证用户是否已被绑定
     * @param userInfo 社交平台用户信息
     * @param platform 平台类型
     * @return 如果已被绑定则返回绑定记录，否则返回null
     */
    CustomerBind validateUserBinding(SocialUserInfo userInfo, String platform);
}
