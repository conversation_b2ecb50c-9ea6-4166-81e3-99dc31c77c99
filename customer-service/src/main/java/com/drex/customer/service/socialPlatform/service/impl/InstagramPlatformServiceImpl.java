package com.drex.customer.service.socialPlatform.service.impl;

import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.AccessToken;
import com.drex.customer.service.socialPlatform.config.SocialPlatformProperties;
import com.drex.customer.service.socialPlatform.constant.SocialConstant;
import com.drex.customer.service.socialPlatform.model.OpenAuthRequest;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.util.HttpPoolUtil;
import com.drex.customer.service.socialPlatform.service.SocialPlatformService;
import com.drex.model.CustomerException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Base64;

/**
 * Instagram平台服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class InstagramPlatformServiceImpl implements SocialPlatformService {

    private static final String INS_AUTH_URL = "https://api.instagram.com/oauth/access_token";

    @Autowired
    private SocialPlatformProperties socialPlatformProperties;

    @Resource
    private CustomerBindService customerBindService;

    @Override
    public String getPlatformType() {
        return SocialConstant.PlatformEnum.Instagram.name();
    }

    @Override
    public String generateAuthUrl() {
        return String.format("%s&client_id=%s&redirect_uri=%s",
                socialPlatformProperties.getInstagram().getAuthScore(),
                socialPlatformProperties.getInstagram().getClientId(),
                socialPlatformProperties.getInstagram().getPcRedirectUri());
    }

    @Override
    public OpenAuthRequest buildAuthRequest(String code) {
        return OpenAuthRequest.builder()
                .url(INS_AUTH_URL)
                .clientId("")
                .query(String.format("code=%s&grant_type=authorization_code&redirect_uri=%s&client_id=%s&client_secret=%s",
                        code,
                        socialPlatformProperties.getInstagram().getPcRedirectUri(),
                        socialPlatformProperties.getInstagram().getClientId(),
                        socialPlatformProperties.getInstagram().getClientSecret()))
                .basic("")
                .build();
    }

    @Override
    public SocialUserInfo getCurrentUser(AccessToken accessToken) throws CustomerException {
        // 使用Instagram API获取用户信息
        String url = "https://graph.instagram.com/" + socialPlatformProperties.getInstagram().getApiVersion() + "/" + accessToken.getUserId() + "?fields=id,username,profile_picture_url&access_token=" + accessToken.getAccessToken();
        Request request = new Request.Builder()
                .url(url)
                .build();
        try {
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("[instagram] currentUser:{}", jsonObject);

            String id = jsonObject.getString("id");
            String username = jsonObject.getString("username");
            String profileImage = jsonObject.getString("profile_picture_url");

            // 转换为统一的用户信息模型
            return SocialUserInfo.builder()
                    .userId(id)
                    .username(username)
                    .profileImageUrl(profileImage)
                    .build();
        } catch (Exception e) {
            log.error("Error getting Instagram user info", e);
            return null;
        }
    }

    @Override
    public CustomerBind validateUserBinding(SocialUserInfo userInfo) {
        if (userInfo == null) {
            return null;
        }

        // Instagram平台使用默认的userId验证
        if (StringUtils.hasText(userInfo.getUserId())) {
            log.info("[InstagramPlatformService] Validating user binding by userId: {}", userInfo.getUserId());
            return customerBindService.findBySocialUserIdOrName(
                userInfo.getUserId(), userInfo.getUsername(), getPlatformType());
        }

        return null;
    }
}
