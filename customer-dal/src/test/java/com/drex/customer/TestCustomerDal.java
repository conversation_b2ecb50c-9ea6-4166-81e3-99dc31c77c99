package com.drex.customer;

import com.alibaba.fastjson.JSON;
import com.drex.customer.dal.tablestore.builder.CustomerBindBuilder;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 * @date 2025/4/29 11:08
 * @description:
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = TestCustomerDal.class)
@TestPropertySource(locations = "classpath:application.properties")
@SpringBootApplication(scanBasePackages = {"com.drex.customer"})
public class TestCustomerDal {

    @Resource
    private CustomerBindBuilder customerBindBuilder;

    @Test
    void contextLoads() {}

    @Test
    public void testSearchSocialUserId() {
        CustomerBind res = customerBindBuilder.findBySocialUserIdOrName("", "", "X");
        System.out.println(JSON.toJSONString(res));
    }
}
