package com.drex.customer.service.socialPlatform.service.impl;

import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.constant.SocialConstant;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.socialPlatform.service.UserBindValidator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * X(Twitter)平台用户绑定验证器实现
 * 使用username字段进行验证
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class XUserBindValidatorImpl implements UserBindValidator {
    
    @Resource
    private CustomerBindService customerBindService;
    
    @Override
    public String getSupportedPlatform() {
        return SocialConstant.PlatformEnum.X.name();
    }
    
    @Override
    public CustomerBind validateUserBinding(SocialUserInfo userInfo, String platform) {
        if (userInfo == null) {
            return null;
        }
        
        // X平台优先使用username进行验证
        if (StringUtils.hasText(userInfo.getUsername())) {
            log.info("[XUserBindValidator] Validating user binding by username: {}", userInfo.getUsername());
            CustomerBind existingBind = customerBindService.findBySocialUserIdOrName(
                userInfo.getUserId(), userInfo.getUsername(), platform);
            if (existingBind != null) {
                log.info("[XUserBindValidator] Found existing binding by username: {}", userInfo.getUsername());
                return existingBind;
            }
        }
        
        // 如果username验证失败，回退到userId验证
        if (StringUtils.hasText(userInfo.getUserId())) {
            log.info("[XUserBindValidator] Fallback to userId validation: {}", userInfo.getUserId());
            return customerBindService.findBySocialUserIdOrName(
                userInfo.getUserId(), userInfo.getUsername(), platform);
        }
        
        return null;
    }
}
