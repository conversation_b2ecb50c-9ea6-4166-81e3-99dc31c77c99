package com.drex.customer.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.drex.customer.api.ErrorCode;
import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.AuthService;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.AccessToken;
import com.drex.customer.service.socialPlatform.model.OpenAuthRequest;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.socialPlatform.model.Token;
import com.drex.customer.service.socialPlatform.service.SocialPlatformService;
import com.drex.customer.service.socialPlatform.service.SocialPlatformServiceFactory;
import com.drex.customer.service.socialPlatform.service.UserBindValidatorFactory;
import com.drex.model.CustomerException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;

/**
 * 认证服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Resource
    private OkHttpClient okHttpClient;
    @Resource
    private CustomerBindService customerBindService;
    @Resource
    private SocialPlatformServiceFactory platformServiceFactory;

    @Override
    public Token auth(String platform, String code, String customerId) throws CustomerException {
        log.info("[AuthServiceImpl] auth platform: {} code: {} customerId: {}", platform, code, customerId);
        // 检查用户是否已绑定该平台
        CustomerBind customerBind = customerBindService.findByCustomerId(customerId, platform);
        if (customerBind != null) {
            log.info("[auth] Platform already bound: {}, {}", platform, customerId);
            Token token = new Token();
            token.setSocialUserId(customerBind.getSocialUserId());
            token.setSocialHandleName(customerBind.getSocialHandleName());
            return token;
        }

        // 获取对应平台的服务
        SocialPlatformService platformService = platformServiceFactory.getService(platform);
        if (platformService == null) {
            log.error("[auth] Platform not supported: {}", platform);
            throw new CustomerException(ErrorCode.UNKNOWN_ERROR);
        }

        // 构建授权请求
        OpenAuthRequest authRequest = platformService.buildAuthRequest(code);

        // 发送授权请求获取访问令牌
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, authRequest.getQuery());
        Request request = new Request.Builder()
                .url(authRequest.getUrl())
                .method("POST", body)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Authorization", "Basic " + authRequest.getBasic())
                .build();

        try {
            Response response = okHttpClient.newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = JSON.parseObject(responseBody);
            log.info("[auth] Auth result: {}, {}, {}", authRequest, jsonObject, customerId);

            if (!response.isSuccessful() || !jsonObject.containsKey("access_token")) {
                throw new CustomerException(ErrorCode.AUTH_CODE_INVALID);
            }

            // 创建令牌对象
            Token token = new Token();
            token.setAccessToken(jsonObject.getString("access_token"));
            token.setRefreshToken(jsonObject.getString("refresh_token"));
            token.setSocialUserId(jsonObject.getString("user_id"));

            // 获取用户信息
            AccessToken accessToken = AccessToken.builder()
                    .accessToken(token.getAccessToken())
                    .refreshToken(token.getRefreshToken())
                    .userId(token.getSocialUserId())
                    .build();

            SocialUserInfo userInfo = platformService.getCurrentUser(accessToken);
            if (userInfo == null) {
                throw new CustomerException(ErrorCode.AUTH_CODE_INVALID);
            }

            // 检查该社交账号是否已被其他用户绑定
            customerBind = customerBindService.findBySocialUserIdOrName(userInfo.getUserId(), userInfo.getUsername(), platform);
            if (customerBind != null) {
                throw new CustomerException(ErrorCode.AUTH_REPEAT);
            }

            // 更新令牌信息
            token.setSocialUserId(userInfo.getUserId());
            token.setSocialHandleName(userInfo.getUsername());

            // 创建绑定记录
            customerBind = new CustomerBind();
            customerBind.setCustomerId(customerId);
            customerBind.setSocialPlatform(platform);
            customerBind.setSocialUserId(token.getSocialUserId());
            customerBind.setSocialHandleName(token.getSocialHandleName());
            customerBind.setSocialProfileImage(userInfo.getProfileImageUrl());

            // 保存绑定记录
            boolean res = customerBindService.insert(customerBind);
            log.info("[auth] Bind social: {}, {}", authRequest, res);

            if (!res) {
                throw new CustomerException(ErrorCode.AUTH_CODE_INVALID);
            }

            return token;
        } catch (CustomerException ex) {
            log.error("[auth] Auth error (customer exception): {}", authRequest, ex);
            throw ex;
        } catch (Exception e) {
            log.error("[auth] Auth error: {}", authRequest, e);
            throw new CustomerException(ErrorCode.AUTH_CODE_INVALID);
        }
    }
}
