package com.drex.customer.service.socialPlatform.service;

import com.drex.customer.service.socialPlatform.service.impl.DefaultUserBindValidatorImpl;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户绑定验证器工厂
 * 管理所有平台的用户绑定验证器实现
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class UserBindValidatorFactory {
    
    @Autowired
    private List<UserBindValidator> validators;
    
    @Autowired
    private DefaultUserBindValidatorImpl defaultValidator;
    
    private final Map<String, UserBindValidator> validatorMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        validators.forEach(validator -> {
            if (!"DEFAULT".equals(validator.getSupportedPlatform())) {
                validatorMap.put(validator.getSupportedPlatform(), validator);
                log.info("Registered user bind validator: {}", validator.getSupportedPlatform());
            }
        });
    }
    
    /**
     * 获取指定平台的验证器
     * @param platform 平台类型
     * @return 验证器实现，如果没有找到则返回默认验证器
     */
    public UserBindValidator getValidator(String platform) {
        UserBindValidator validator = validatorMap.get(platform);
        if (validator == null) {
            log.info("No specific validator found for platform: {}, using default validator", platform);
            return defaultValidator;
        }
        return validator;
    }
}
