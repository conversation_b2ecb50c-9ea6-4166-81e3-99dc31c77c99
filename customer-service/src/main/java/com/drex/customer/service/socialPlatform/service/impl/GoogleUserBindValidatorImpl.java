package com.drex.customer.service.socialPlatform.service.impl;

import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.constant.SocialConstant;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.socialPlatform.service.UserBindValidator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Google平台用户绑定验证器实现
 * 使用email字段进行验证
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class GoogleUserBindValidatorImpl implements UserBindValidator {
    
    @Resource
    private CustomerBindService customerBindService;
    
    @Override
    public String getSupportedPlatform() {
        return SocialConstant.PlatformEnum.Google.name();
    }
    
    @Override
    public CustomerBind validateUserBinding(SocialUserInfo userInfo, String platform) {
        if (userInfo == null) {
            return null;
        }
        
        // Google平台优先使用email进行验证
        if (StringUtils.hasText(userInfo.getEmail())) {
            log.info("[GoogleUserBindValidator] Validating user binding by email: {}", userInfo.getEmail());
            // 这里需要扩展CustomerBindService来支持按email查询
            // 暂时使用username字段存储email，后续可以考虑添加专门的email字段
            CustomerBind existingBind = customerBindService.findBySocialUserIdOrName(
                userInfo.getUserId(), userInfo.getEmail(), platform);
            if (existingBind != null) {
                log.info("[GoogleUserBindValidator] Found existing binding by email: {}", userInfo.getEmail());
                return existingBind;
            }
        }
        
        // 如果email验证失败，回退到userId验证
        if (StringUtils.hasText(userInfo.getUserId())) {
            log.info("[GoogleUserBindValidator] Fallback to userId validation: {}", userInfo.getUserId());
            return customerBindService.findBySocialUserIdOrName(
                userInfo.getUserId(), userInfo.getUsername(), platform);
        }
        
        return null;
    }
}
