package com.drex.customer.service.socialPlatform.service.impl;

import com.drex.customer.dal.tablestore.model.CustomerBind;
import com.drex.customer.service.business.CustomerBindService;
import com.drex.customer.service.socialPlatform.AccessToken;
import com.drex.customer.service.socialPlatform.config.SocialPlatformProperties;
import com.drex.customer.service.socialPlatform.constant.SocialConstant;
import com.drex.customer.service.socialPlatform.model.OpenAuthRequest;
import com.drex.customer.service.socialPlatform.model.SocialUserInfo;
import com.drex.customer.service.util.HttpPoolUtil;
import com.drex.customer.service.socialPlatform.service.SocialPlatformService;
import com.drex.model.CustomerException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Base64;

/**
 * TikTok平台服务实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TiktokPlatformServiceImpl implements SocialPlatformService {

    private static final String TIKTOK_AUTH_URL = "https://open.tiktokapis.com/v2/oauth/token/";

    @Autowired
    private SocialPlatformProperties socialPlatformProperties;

    @Resource
    private CustomerBindService customerBindService;

    @Override
    public String getPlatformType() {
        return SocialConstant.PlatformEnum.TikTok.name();
    }

    @Override
    public String generateAuthUrl() {
        return String.format("%s&client_key=%s&redirect_uri=%s",
                socialPlatformProperties.getTiktok().getAuthScore(),
                socialPlatformProperties.getTiktok().getClientId(),
                socialPlatformProperties.getTiktok().getPcRedirectUri());
    }

    @Override
    public OpenAuthRequest buildAuthRequest(String code) {
        return OpenAuthRequest.builder()
                .url(TIKTOK_AUTH_URL)
                .clientId("")
                .query(String.format("code=%s&grant_type=authorization_code&redirect_uri=%s&client_key=%s&client_secret=%s",
                        code,
                        socialPlatformProperties.getTiktok().getPcRedirectUri(),
                        socialPlatformProperties.getTiktok().getClientId(),
                        socialPlatformProperties.getTiktok().getClientSecret()))
                .basic("")
                .build();
    }

    @Override
    public SocialUserInfo getCurrentUser(AccessToken accessToken) throws CustomerException {
        // 使用TikTok API获取用户信息
        Request request = new Request.Builder()
                .url("https://open.tiktokapis.com/v2/user/info/?fields=open_id,avatar_url,username")
                .addHeader("Authorization", "Bearer " + accessToken.getAccessToken())
                .build();

        try {
            Response response = HttpPoolUtil.getHttpClient().newCall(request).execute();
            String responseBody = response.body().string();
            JSONObject jsonObject = new JSONObject(responseBody);
            log.info("[tiktok] currentUser:{}", jsonObject);
            JSONObject data = jsonObject.getJSONObject("data");
            JSONObject userObject = data.getJSONObject("user");

            String id = userObject.getString("open_id");
            String username = userObject.getString("username");
            String profileImage = userObject.getString("avatar_url");

            // 转换为统一的用户信息模型
            return SocialUserInfo.builder()
                    .userId(id)
                    .username(username)
                    .profileImageUrl(profileImage)
                    .build();
        } catch (Exception e) {
            log.error("Error getting TikTok user info", e);
            return null;
        }
    }

    @Override
    public CustomerBind validateUserBinding(SocialUserInfo userInfo) {
        if (userInfo == null) {
            return null;
        }

        // TikTok平台使用默认的userId验证
        if (StringUtils.hasText(userInfo.getUserId())) {
            log.info("[TiktokPlatformService] Validating user binding by userId: {}", userInfo.getUserId());
            return customerBindService.findBySocialUserIdOrName(
                userInfo.getUserId(), userInfo.getUsername(), getPlatformType());
        }

        return null;
    }
}
